import React, { useState, useCallback, useMemo, useRef } from 'react';
import { useDrag } from 'react-dnd';
import { Steps, Button, Form, Input, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { STEPS_CONTAINER } from '../../../constants';
import DropZone from '../DropZone';
import Component from '../Component';
import styled from 'styled-components';

// Styled component for inline editable text
const InlineEditableText = styled.span`
  .editable-text {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
    display: inline-block;
    min-width: 20px;

    &:hover {
      background-color: rgba(24, 144, 255, 0.1);
      border: 1px solid rgba(24, 144, 255, 0.3);
    }

    &.placeholder-text {
      color: #999;
      font-style: italic;
    }
  }

  .editing-input {
    border: 1px solid #1890ff;
    border-radius: 3px;
    padding: 2px 4px;
    outline: none;
    background: white;
    box-shadow: 0 0 4px rgba(24, 144, 255, 0.3);
    min-width: 100px;
  }
`;

// Memoized StepsContainer component for multi-step forms with validation
const StepsContainer = React.memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);
    const [currentStep, setCurrentStep] = useState(0);
    const [isEditModalVisible, setIsEditModalVisible] = useState(false);
    const [editingStep, setEditingStep] = useState(null);
    const [form] = Form.useForm();

    // Inline editing state
    const [isEditing, setIsEditing] = useState(false);
    const [editingValue, setEditingValue] = useState('');
    const [originalValue, setOriginalValue] = useState('');
    const [editingStepIndex, setEditingStepIndex] = useState(null);
    const [editingProperty, setEditingProperty] = useState(null);

    // Memoized drag item
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: STEPS_CONTAINER,
        steps: data?.steps || [],
        path: path || `steps-${data?.id}`,
      }),
      [data?.id, data?.steps, path],
    );

    // Simplified component renderer - use same pattern as CardContainer
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    const [{ isDragging }, drag] = useDrag({
      type: STEPS_CONTAINER,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Enhanced container style with visual feedback
    const containerStyle = useMemo(
      () => ({
        opacity: isDragging ? 0.5 : 1,
        margin: '8px 0',
        border: isDragging ? '2px dashed #1890ff' : '1px solid #f0f0f0',
        borderRadius: '8px',
        background: isDragging ? '#f8f9ff' : '#fafafa',
        transition: 'all 0.2s ease',
        transform: isDragging ? 'scale(1.02)' : 'scale(1)',
        boxShadow: isDragging
          ? '0 4px 12px rgba(24, 144, 255, 0.15)'
          : '0 1px 3px rgba(0, 0, 0, 0.1)',
      }),
      [isDragging],
    );

    // Get steps from layout data
    const component = components[data.id] || {};

    // Default steps if none are provided (memoized to prevent re-creation)
    const defaultSteps = useMemo(
      () => [
        {
          id: `step_${data.id}_1`,
          key: `step_${data.id}_1`,
          title: 'Step 1',
          description: 'First step',
          children: [],
        },
        {
          id: `step_${data.id}_2`,
          key: `step_${data.id}_2`,
          title: 'Step 2',
          description: 'Second step',
          children: [],
        },
      ],
      [data.id],
    );

    // Memoize steps to prevent unnecessary re-renders
    // ALWAYS prioritize component registry for updated data (inline edits)
    const steps = useMemo(() => {
      const existingSteps = component.steps || data.steps;
      console.log('🔄 [StepsContainer Builder] Data sync check:', {
        containerId: data.id,
        existingSteps,
        componentSteps: component.steps,
        dataSteps: data.steps,
        componentStepsLength: component.steps?.length || 0,
        dataStepsLength: data.steps?.length || 0,
        usingStepsLength: existingSteps?.length || 0,
        willUseDefault: !existingSteps || existingSteps.length === 0,
      });

      if (!existingSteps || existingSteps.length === 0) {
        console.log('StepsContainer - using defaultSteps:', defaultSteps);
        console.log(
          'StepsContainer - defaultSteps IDs:',
          defaultSteps.map((s) => s.id),
        );
        return defaultSteps;
      }
      console.log('StepsContainer - using existingSteps:', existingSteps);
      console.log(
        'StepsContainer - existingSteps IDs:',
        existingSteps.map((s) => s.id),
      );
      return existingSteps;
    }, [component.steps, data.steps, defaultSteps]);

    // Initialize steps if they don't exist
    React.useEffect(() => {
      const existingSteps = component.steps || data.steps;
      if (
        (!existingSteps || existingSteps.length === 0) &&
        onUpdateComponent &&
        data.id
      ) {
        onUpdateComponent(data.id, { steps: defaultSteps });
      }
    }, [data.id, data.steps, component.steps, onUpdateComponent, defaultSteps]);

    // Step management functions
    const handleAddStep = useCallback(() => {
      const currentSteps = steps || [];
      const stepNumber = currentSteps.length + 1;
      const timestamp = Date.now();

      const newStep = {
        id: `step_${timestamp}`,
        key: `step_${timestamp}`,
        title: `Step ${stepNumber}`,
        description: `Step ${stepNumber} description`,
        children: [],
      };

      const updatedSteps = [...currentSteps, newStep];

      // Update both the layout and component state
      if (onUpdateComponent && data.id) {
        onUpdateComponent(data.id, { steps: updatedSteps });
      }
    }, [data.id, steps, onUpdateComponent]);

    const handleEditStep = useCallback(
      (step) => {
        setEditingStep(step);
        form.setFieldsValue({
          title: step.title,
          description: step.description,
        });
        setIsEditModalVisible(true);
      },
      [form],
    );

    const handleDeleteStep = useCallback(
      (stepKey) => {
        if (steps.length <= 2) return; // Keep at least two steps

        const updatedSteps = steps.filter((step) => step.key !== stepKey);
        onUpdateComponent?.(data.id, { steps: updatedSteps });

        // Adjust current step if necessary
        if (currentStep >= updatedSteps.length) {
          setCurrentStep(Math.max(0, updatedSteps.length - 1));
        }
      },
      [data.id, steps, currentStep, onUpdateComponent],
    );

    const handleSaveStep = useCallback(() => {
      form.validateFields().then((values) => {
        const updatedSteps = steps.map((step) =>
          step.id === editingStep.id
            ? { ...step, title: values.title, description: values.description }
            : step,
        );
        onUpdateComponent?.(data.id, { steps: updatedSteps });
        setIsEditModalVisible(false);
        setEditingStep(null);
        form.resetFields();
      });
    }, [form, steps, editingStep, data.id, onUpdateComponent]);

    // Inline editing functions
    const handleLabelClick = useCallback(
      (e, currentText, stepIndex, property) => {
        e.stopPropagation();
        setIsEditing(true);
        setEditingValue(currentText || '');
        setOriginalValue(currentText || '');
        setEditingStepIndex(stepIndex);
        setEditingProperty(property);
      },
      [],
    );

    // Define editing handlers first (before they're used in other callbacks)
    const handleEditingSave = useCallback(() => {
      if (
        onUpdateComponent &&
        editingValue !== originalValue &&
        editingStepIndex !== null &&
        editingProperty
      ) {
        const updatedSteps = steps.map((step, index) =>
          index === editingStepIndex
            ? { ...step, [editingProperty]: editingValue }
            : step,
        );
        onUpdateComponent(data.id, { steps: updatedSteps });
      }
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingStepIndex(null);
      setEditingProperty(null);
    }, [
      onUpdateComponent,
      data.id,
      editingValue,
      originalValue,
      editingStepIndex,
      editingProperty,
      steps,
    ]);

    const handleEditingCancel = useCallback(() => {
      setIsEditing(false);
      setEditingValue('');
      setOriginalValue('');
      setEditingStepIndex(null);
      setEditingProperty(null);
    }, []);

    const handleEditingKeyDown = useCallback(
      (e) => {
        if (e.key === 'Enter') {
          handleEditingSave();
        } else if (e.key === 'Escape') {
          handleEditingCancel();
        }
      },
      [handleEditingSave, handleEditingCancel],
    );

    const handleEditingBlur = useCallback(() => {
      handleEditingSave();
    }, [handleEditingSave]);

    // Helper function to render editable text
    const renderEditableText = useCallback(
      (text, stepIndex, property, placeholder = 'Click to edit') => {
        const displayText = text || placeholder;
        const isPlaceholder = !text;
        const isCurrentlyEditing =
          isEditing &&
          editingStepIndex === stepIndex &&
          editingProperty === property;

        if (isCurrentlyEditing) {
          return (
            <input
              className='editing-input'
              value={editingValue}
              onChange={(e) => setEditingValue(e.target.value)}
              onKeyDown={handleEditingKeyDown}
              onBlur={handleEditingBlur}
              autoFocus
              style={{
                fontSize: 'inherit',
                fontFamily: 'inherit',
                fontWeight: 'inherit',
                color: 'inherit',
              }}
            />
          );
        }

        return (
          <InlineEditableText>
            <span
              className={`editable-text ${
                isPlaceholder ? 'placeholder-text' : ''
              }`}
              onClick={(e) => handleLabelClick(e, text, stepIndex, property)}
              title='Click to edit'
            >
              {displayText}
            </span>
          </InlineEditableText>
        );
      },
      [
        isEditing,
        editingValue,
        editingStepIndex,
        editingProperty,
        handleEditingKeyDown,
        handleEditingBlur,
        handleLabelClick,
      ],
    );

    // Render step content with nested support
    const renderStepContent = useCallback(
      (step, stepIndex) => {
        if (!step) {
          return (
            <div
              style={{
                minHeight: '200px',
                padding: '16px',
                textAlign: 'center',
                color: '#999',
              }}
            >
              Step not found
            </div>
          );
        }

        // Enhanced children retrieval with components registry sync
        const children = step.children || [];

        console.log('🔍 [StepsContainer] Step children debug:', {
          stepId: step.id,
          stepIndex,
          stepChildren: children.length,
          stepChildrenIds: children.map((c) => c.id),
          componentsRegistrySize: Object.keys(components).length,
          componentsInRegistry: Object.keys(components),
        });

        // Ensure we get the latest component data from the registry
        const enrichedChildren = children.map((child) => {
          const componentData = components[child.id];
          if (componentData) {
            // Merge layout structure with component registry data
            return {
              ...child,
              ...componentData,
              // Preserve layout-specific properties
              children: child.children,
            };
          }
          return child;
        });

        return (
          <div
            style={{
              minHeight: '200px',
              padding: '16px',
              border: '1px solid #f0f0f0',
              borderRadius: '6px',
              background: '#fafafa',
            }}
          >
            {/* Always show empty state drop zone when no children */}
            {enrichedChildren.length === 0 ? (
              <DropZone
                data={{
                  path: `${path}-step-${stepIndex}-0`,
                  childrenCount: 0,
                  containerId: data.id,
                  stepId: step.id,
                  containerType: 'step-content',
                }}
                onDrop={(draggedItem, dropData) => {
                  console.log(
                    '🎯 [StepsContainer] DropZone data being passed:',
                    {
                      stepId: step.id,
                      stepIndex,
                      containerId: data.id,
                      dropData,
                      draggedItem,
                    },
                  );
                  handleDrop(draggedItem, dropData);
                }}
                className='empty-step-drop-zone'
                style={{
                  minHeight: '180px',
                  border: '2px dashed #d9d9d9',
                  borderRadius: '6px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#999',
                  background: '#fff',
                  fontSize: '14px',
                }}
              >
                <div style={{ textAlign: 'center' }}>
                  <div style={{ marginBottom: '4px' }}>📋</div>
                  <div>Drop components here for Step {stepIndex + 1}</div>
                </div>
              </DropZone>
            ) : (
              // Enhanced rendering with main canvas-like behavior
              <div style={{ minHeight: '180px' }}>
                {enrichedChildren.map((component, index) => {
                  const currentPath = `${path}-step-${stepIndex}-${index}`;

                  // Enhanced drop zone data with proper path structure
                  const dropZoneData = {
                    path: currentPath,
                    childrenCount: enrichedChildren.length,
                    containerId: data.id,
                    stepId: step.id,
                    containerType: 'step-content',
                  };

                  return (
                    <React.Fragment key={component.id || index}>
                      {/* Drop zone before each component - using standard DropZone */}
                      <DropZone
                        data={dropZoneData}
                        onDrop={handleDrop}
                        className={
                          component.type === 'row'
                            ? 'horizontalDrag'
                            : 'step-content-drop-zone'
                        }
                      />
                      {/* Enhanced component rendering with proper layout support */}
                      {renderComponent(component, currentPath)}
                    </React.Fragment>
                  );
                })}

                {/* Final drop zone after all components - using standard DropZone */}
                <DropZone
                  data={{
                    path: `${path}-step-${stepIndex}-${enrichedChildren.length}`,
                    childrenCount: enrichedChildren.length,
                    containerId: data.id,
                    stepId: step.id,
                    containerType: 'step-content',
                  }}
                  onDrop={handleDrop}
                  className='step-content-drop-zone-final'
                  isLast
                />
              </div>
            )}
          </div>
        );
      },
      [path, data.id, handleDrop, renderComponent, components],
    );

    // Memoized step items for Ant Design Steps
    const stepItems = useMemo(() => {
      return steps.map((step, index) => ({
        key: step.key,
        title: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {renderEditableText(
              step.title,
              index,
              'title',
              `Step ${index + 1}`,
            )}
            <div style={{ display: 'flex', gap: '4px' }}>
              <Button
                type='text'
                size='small'
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditStep(step);
                }}
                style={{ padding: '0 4px' }}
              />
              {steps.length > 2 && (
                <Button
                  type='text'
                  size='small'
                  icon={<DeleteOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteStep(step.key);
                  }}
                  style={{ padding: '0 4px', color: '#ff4d4f' }}
                />
              )}
            </div>
          </div>
        ),
        description: renderEditableText(
          step.description,
          index,
          'description',
          'Step description',
        ),
      }));
    }, [steps, handleEditStep, handleDeleteStep, renderEditableText]);

    drag(ref);

    return (
      <div ref={ref} style={containerStyle}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '8px 16px',
            background: '#fafafa',
            borderBottom: '1px solid #d9d9d9',
          }}
        >
          <span style={{ fontWeight: '500', color: '#666' }}>
            Steps Container
          </span>
          <Button
            type='dashed'
            size='small'
            icon={<PlusOutlined />}
            onClick={handleAddStep}
          >
            Add Step
          </Button>
        </div>

        <div style={{ padding: '16px' }}>
          {steps.length > 0 ? (
            <>
              <Steps
                current={currentStep}
                onChange={(step) => {
                  setCurrentStep(step);
                }}
                items={stepItems}
                direction='horizontal'
                size='small'
                type='navigation'
                style={{ cursor: 'pointer' }}
              />

              <div style={{ marginTop: '16px' }}>
                {steps[currentStep] &&
                  renderStepContent(steps[currentStep], currentStep)}
              </div>

              {/* Step navigation */}
              <div
                style={{
                  marginTop: '16px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  borderTop: '1px solid #f0f0f0',
                  paddingTop: '16px',
                }}
              >
                <Button
                  disabled={currentStep === 0}
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  Previous
                </Button>
                <Button
                  type='primary'
                  disabled={currentStep === steps.length - 1}
                  onClick={() => setCurrentStep(currentStep + 1)}
                >
                  Next
                </Button>
              </div>
            </>
          ) : (
            <div
              style={{
                textAlign: 'center',
                padding: '40px 20px',
                color: '#999',
                border: '2px dashed #d9d9d9',
                borderRadius: '4px',
                background: '#fafafa',
              }}
            >
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>
                No steps defined
              </div>
              <div style={{ fontSize: '14px' }}>
                Click &quot;Add Step&quot; to create your first step
              </div>
            </div>
          )}
        </div>

        {/* Edit Step Modal */}
        <Modal
          title='Edit Step'
          open={isEditModalVisible}
          onOk={handleSaveStep}
          onCancel={() => {
            setIsEditModalVisible(false);
            setEditingStep(null);
            form.resetFields();
          }}
          width={400}
        >
          <Form form={form} layout='vertical'>
            <Form.Item
              name='title'
              label='Step Title'
              rules={[{ required: true, message: 'Please enter step title' }]}
            >
              <Input placeholder='Enter step title...' />
            </Form.Item>
            <Form.Item name='description' label='Step Description'>
              <Input placeholder='Enter step description...' />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance - avoid JSON.stringify for deep comparison
    if (
      prevProps.data?.id !== nextProps.data?.id ||
      prevProps.path !== nextProps.path ||
      prevProps.handleDrop !== nextProps.handleDrop ||
      prevProps.onUpdateComponent !== nextProps.onUpdateComponent ||
      prevProps.components !== nextProps.components
    ) {
      return false;
    }

    // Compare steps array length and basic properties only (shallow comparison)
    const prevSteps = prevProps.data?.steps || [];
    const nextSteps = nextProps.data?.steps || [];

    if (prevSteps.length !== nextSteps.length) {
      return false;
    }

    // Compare step IDs and titles only (avoid deep comparison)
    for (let i = 0; i < prevSteps.length; i++) {
      if (
        prevSteps[i]?.id !== nextSteps[i]?.id ||
        prevSteps[i]?.key !== nextSteps[i]?.key ||
        prevSteps[i]?.title !== nextSteps[i]?.title ||
        prevSteps[i]?.children?.length !== nextSteps[i]?.children?.length
      ) {
        return false;
      }
    }

    return true;
  },
);

StepsContainer.displayName = 'StepsContainer';

export default StepsContainer;
