import { useRef, memo, useMemo, useCallback, Fragment } from 'react';
import { useDrag } from 'react-dnd';
import { COLUMN } from '../../constants';
import DropZone from './DropZone';
import Component from './Component';
import * as S from '../../styles';
import { useDoubleClickHandler } from '../FormBuilderApp/components/PropertiesPanel';

// Memoized Column component for better performance
const Column = memo(
  ({ data, components, handleDrop, path, onUpdateComponent }) => {
    const ref = useRef(null);

    // Double-click handler for properties panel
    const { getContainerDoubleClickProps } = useDoubleClickHandler({
      componentData: data,
      componentId: data?.id,
      components,
    });

    // Enhanced children retrieval with components registry sync
    const layoutChildren = data?.children || [];

    // Ensure we get the latest component data from the registry for nested components
    const children = layoutChildren.map((child) => {
      const componentData = components[child.id];
      if (componentData) {
        // Merge layout structure with component registry data
        return {
          ...child,
          ...componentData,
          // Preserve layout-specific properties
          children: child.children,
        };
      }
      return child;
    });

    // Memoized drag item to prevent unnecessary re-creations - using enriched children
    const dragItem = useMemo(
      () => ({
        id: data?.id,
        type: COLUMN,
        children: children,
        path,
      }),
      [data?.id, data?.children, path, components, children],
    );

    // Memoized component renderer for better performance
    const renderComponent = useCallback(
      (component, currentPath) => {
        return (
          <Component
            key={component.id}
            data={component}
            components={components}
            path={currentPath}
            handleDrop={handleDrop}
            onUpdateComponent={onUpdateComponent}
          />
        );
      },
      [components, handleDrop, onUpdateComponent],
    );

    const [{ isDragging }, drag] = useDrag({
      type: COLUMN,
      item: dragItem,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Memoized style to prevent unnecessary re-computations
    const columnStyle = useMemo(
      () => ({
        opacity: isDragging ? 0 : 1,
      }),
      [isDragging],
    );

    // Memoized className to prevent unnecessary re-computations
    const className = useMemo(
      () => `draggable ${isDragging ? 'dragging' : ''}`,
      [isDragging],
    );

    // Memoized last drop zone data
    const lastDropZoneData = useMemo(
      () => ({
        path: `${path}-${children.length}`,
        childrenCount: children.length,
      }),
      [path, children.length],
    );

    // Add safety check for data after all hooks
    if (!data || !data.id) {
      console.error('Column component received invalid data:', data);
      return null;
    }

    drag(ref);

    return (
      <S.Column
        ref={ref}
        style={columnStyle}
        className={className}
        {...getContainerDoubleClickProps()}
      >
        {children.map((component, index) => {
          const currentPath = `${path}-${index}`;

          // Memoized drop zone data for each component
          const dropZoneData = {
            path: currentPath,
            childrenCount: children.length,
          };

          return (
            <Fragment key={component.id}>
              <DropZone data={dropZoneData} onDrop={handleDrop} />
              {renderComponent(component, currentPath)}
            </Fragment>
          );
        })}
        <DropZone data={lastDropZoneData} onDrop={handleDrop} isLast />
      </S.Column>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison function to ensure re-render when path changes - avoid JSON.stringify
    if (
      prevProps.data?.id !== nextProps.data?.id ||
      prevProps.path !== nextProps.path ||
      prevProps.handleDrop !== nextProps.handleDrop ||
      prevProps.components !== nextProps.components ||
      prevProps.onUpdateComponent !== nextProps.onUpdateComponent
    ) {
      return false;
    }

    // Compare children array length and IDs only (shallow comparison)
    const prevChildren = prevProps.data?.children || [];
    const nextChildren = nextProps.data?.children || [];

    if (prevChildren.length !== nextChildren.length) {
      return false;
    }

    // Compare children IDs and types only (avoid deep comparison)
    for (let i = 0; i < prevChildren.length; i++) {
      if (
        prevChildren[i]?.id !== nextChildren[i]?.id ||
        prevChildren[i]?.type !== nextChildren[i]?.type
      ) {
        return false;
      }
    }

    return true;
  },
);

// Set display name for debugging
Column.displayName = 'Column';

export default Column;
